"use client";
import { useEffect, useState } from "react";
import { GlobalCardEvents } from "@/globalComponents/globalCardEvents";
import { getEventsByCategory } from "@/services/eventsServices";
import { getUserByEventId } from "@/services/usersServices";
import { themes } from "../../../../theme";
import { useRouter } from "next/navigation";
import useAuth from "@/hook";
import { AlertCircle } from "react-feather";
import EventCardSkeleton from "@/components/CardSkeleton/EventCardSkeleton";
import { useFilter } from "@/context/FilterContext";

const EventsCard = (props: any) => {
  const router = useRouter();
  const user = useAuth();
  const { filters, getServiceFilters } = useFilter();
  const [events, setEvents] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [userLoading, setUserLoading] = useState<string | null>(null);

  useEffect(() => {
    const fetchAllEvents = async () => {
      setLoading(true);
      setError(null);
      try {
        if (!props.themeProperties?.title) {
          throw new Error("Category title is missing");
        }

        const currentCategory =
          props.themeProperties.title === "My Feed" ? "My Feed" : props.themeProperties.title;

        // Check if filters are applied and if current category is in selected categories
        if (filters.categories && filters.categories.length > 0) {
          if (!filters.categories.includes(currentCategory)) {
            // Current category is not in selected filters, show no data
            setEvents([]);
            setLoading(false);
            return;
          }
        }

        // Get complete filter object for service call
        const serviceFilters = getServiceFilters();

        // Fetch events for the current category with all filters
        const responsedan = await getEventsByCategory(
          currentCategory,
          user?.userId,
          serviceFilters
        );

        // Handle different return types from getEventsByCategory
        let eventsData: any[] = [];
        if (Array.isArray(responsedan)) {
          // For "My Feed", it returns events directly as an array
          eventsData = responsedan;
        } else if (responsedan && typeof responsedan === "object" && "success" in responsedan) {
          // For other categories, it returns { success: boolean, events: array }
          if (!responsedan.success) {
            throw new Error(responsedan.error || "Failed to fetch events");
          }
          eventsData = responsedan.events || [];
        } else {
          throw new Error("Unexpected response format from getEventsByCategory");
        }

        if (props.themeProperties.title === "My Feed") {
          console.log(responsedan);
        }
        setEvents(eventsData);
      } catch (err: any) {
        console.error("Error fetching events:", err);
        setError(err.message || "An error occurred while fetching events");
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };
    fetchAllEvents();
  }, [props, user?.userId, retryCount, filters]);

  const handleRetry = () => {
    setRetryCount((prev) => prev + 1);
  };

  const handleEventClick = async (eventId: string) => {
    setUserLoading(eventId);
    try {
      const response = await getUserByEventId(eventId);
      setUserLoading(null);
      if (response.success && response.users && response.users.length > 0) {
        const userData = response.users[0];
        if (userData.profile_name) {
          router.push(`/profile/amuzn/${userData?.profile_name?.replace(/\s+/g, "-")}?view=Events`);
        } else {
          alert("User found but profile name is missing.");
        }
      } else {
        alert("User not found for this event.");
      }
    } catch (error) {
      setUserLoading(null);
      console.error("Error fetching user:", error);
      alert("Error occurred while fetching user information.");
    }
  };

  if (loading) {
    return (
      <div className="">
        {[1, 2, 3].map((num) => (
          <div key={num} className="mb-4">
            <EventCardSkeleton count={1} showGrid={false} />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full mt-2 p-4 bg-red-50 rounded-md border border-red-200">
        <div className="flex items-center gap-2 text-red-600 mb-2">
          <AlertCircle size={20} />
          <span className="font-medium">Error loading events</span>
        </div>
        <p className="text-red-600 text-sm mb-3">{error}</p>
        <button
          onClick={handleRetry}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!events || events.length === 0) {
    return (
      <div className="w-full mt-2 p-4 bg-gray-50 rounded-md border border-gray-200 text-center">
        <p className="text-gray-500">No events found in this category.</p>
      </div>
    );
  }

  return (
    <div className="w-full mt-2">
      {events.map((item: any, index: number) => (
        <div key={index} className="mb-2">
          {Object.entries(themes).map(([_, innerThemeProperties]) => (
            <div key={innerThemeProperties.title}>
              {(item.category === "Storytelling" ? "Literature" : item.category) ===
                innerThemeProperties.title && (
                <button
                  onClick={() => handleEventClick(item.id)}
                  className="w-full text-left cursor-pointer"
                  disabled={userLoading === item.id}
                  style={{ opacity: userLoading === item.id ? 0.6 : 1 }}
                >
                  <div className="w-[350px]">
                    <GlobalCardEvents post={item} border={innerThemeProperties.backgroundColor} />
                  </div>
                </button>
              )}
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default EventsCard;
