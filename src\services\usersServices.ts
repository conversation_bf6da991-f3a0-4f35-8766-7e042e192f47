import { doc, getDoc, setDoc, updateDoc, collection, getDocs } from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";
import { query, where } from "firebase/firestore";
import { User } from "./UserInterface";
import { getAuth } from "firebase/auth";
import { NotificationManager } from "./notificationService";
import { filterPostsByPublishingDate } from "./postService";
import { GetOrderDetailsByUserId } from "./ordersServices";
import { ChatManager } from "./chatService";
import { PublishingDateFilter } from "./filtersServices";
import { MailServiceManager } from "./MailService";
import { GATE_URL, getIdToken } from "@/lib/utils";

export interface GetFiltersInput {
  category?: string[];
  user_id?: string[];
  location?: string[];
  date_of_publishing?: PublishingDateFilter;
}

// Simple cache for frequently accessed user data
const userCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Helper function to check if cache is valid
const isCacheValid = (timestamp: number): boolean => {
  return Date.now() - timestamp < CACHE_DURATION;
};

// Helper function to get cached data
const getCachedData = (key: string): any | null => {
  const cached = userCache.get(key);
  if (cached && isCacheValid(cached.timestamp)) {
    return cached.data;
  }
  userCache.delete(key);
  return null;
};

// Helper function to set cached data
const setCachedData = (key: string, data: any): void => {
  userCache.set(key, { data, timestamp: Date.now() });
};

// **Create User Service**
export const createUser = async (userId: string, userData: any) => {
  try {
    const { db } = await initFirebase();

    const usersCollection = collection(db, "users");
    const userRef = doc(db, "users", userId);
    const resp = await setDoc(userRef, userData);
    return { success: true };
  } catch (error) {
    console.error("Error creating user:", error);
    return { success: false, error: "Server error" };
  }
};
// **Get All Users Service**

export const userFilterWrapper = async ({
  filters,
  users,
}: {
  filters: GetFiltersInput;
  users: User[];
}): Promise<any> => {
  try {
    let finalResp: any[] = [];
    let filterApplied: boolean = false;

    if (filters?.date_of_publishing) {
      users = filterPostsByPublishingDate(users, filters?.date_of_publishing, "users") as User[];
    }

    if (filters?.category?.length || filters?.location?.length || filters?.user_id?.length) {
      filterApplied = true;
      for (let i = 0; i < users?.length; i++) {
        let current = users?.[i];

        // category filter
        if (filters?.category?.length) {
          if (filters?.category?.includes("Literature")) {
            filters.category = [...filters?.category, "Storytelling"];
          }
          let filterCat: string[] = filters.category;
          let userCat: string[] = current?.categories ?? [];

          let mp: { [k: string]: number } = {};

          filterCat?.forEach((curr) => (mp[curr] ? (mp[curr] += 1) : (mp[curr] = 1)));

          userCat?.forEach((curr) => (mp[curr] ? (mp[curr] += 1) : (mp[curr] = 1)));

          if (Object.values(mp).sort()?.[Object.values(mp)?.length - 1] > 1) {
            finalResp?.push(current);
          }
        }

        // location filter
        if (filters?.location?.length) {
          if (filters?.location?.includes(current?.location)) {
            finalResp?.push(current);
          }
        }

        // user_id
        if (filters?.user_id?.length) {
          // @ts-ignore
          if (current?.id && filters?.user_id?.includes(current?.id)) {
            console.log({ current });
            finalResp?.push(current);
          }
        }
      }
    }

    if (!filterApplied) {
      finalResp = users;
    }

    return finalResp;
  } catch (error) {}
};

export const getAllUsers = async (filters?: Omit<GetFiltersInput, "date_of_publishing">) => {
  try {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "GetAllUsers",
        payload: {
          filters,
        },
      }),
    });

    if (response.status !== 200) {
      const errorText = await response.text();
      console.error("getAllUsers error:", errorText);
      return { success: false, error: "Server error" };
    }

    const usersList = await response.json();
    return { success: true, users: usersList?.message };
  } catch (error) {
    console.error("Error fetching users:", error);
    return { success: false, error: "Server error" };
  }
};

// **Get User by ID Service**

let activeGetUserByIdController: AbortController | null = null;

export const getUserById = async (
  userId: string
): Promise<{
  success: boolean;
  user: User;
}> => {
  // Check cache first
  const cacheKey = `user_${userId}`;
  const cachedData = getCachedData(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  if (activeGetUserByIdController) {
    activeGetUserByIdController.abort();
  }

  const controller = new AbortController();
  activeGetUserByIdController = controller;

  try {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "GetUserById",
        payload: {
          userId,
        },
      }),
    });

    if (response.status !== 200) {
      const errorText = await response.text();
      console.error("getUserById error:", errorText);
      return { success: false, user: {} as User };
    }

    const resp = (await response.json())?.message;

    if (resp?.user?.currency) {
      localStorage.setItem("currency", JSON.stringify(resp.user.currency));
    } else {
      localStorage.setItem("currency", "gbp");
    }

    // Cache the successful response
    setCachedData(cacheKey, resp);
    return resp;
  } catch (error) {
    console.error("getUserById error:", error);
    return { success: false, user: {} as User };
  } finally {
    if (activeGetUserByIdController === controller) {
      activeGetUserByIdController = null;
    }
  }
};

export const getUsersByIds = async (
  userIds: string[],
  filters?: Omit<GetFiltersInput, "date_of_publishing">
): Promise<{
  success: boolean;
  users: Array<User & { posts: any[]; following: string[]; followers: string[] }>;
}> => {
  try {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "GetUsersByIds",
        payload: {
          userIds,
          filters,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = await response.json();
    return resp?.message;

    // let usersData: any[] = [];
    // const { db } = await initFirebase();

    // for (const userId of userIds) {
    //   const userRef = doc(db, "users", userId);
    //   const userSnap = await getDoc(userRef);

    //   if (!userSnap.exists()) continue;

    //   const userData = userSnap.data() as User;

    //   // Fetch post details using post IDs
    //   if (userData?.posts && userData?.posts.length > 0) {
    //     const postPromises = userData?.posts?.map(async (postId: string) => {
    //       const postRef = doc(db, "posts", postId);
    //       const postSnapshot = await getDoc(postRef);

    //       return postSnapshot.exists() ? { id: postSnapshot.id, ...postSnapshot.data() } : null;
    //     });

    //     const posts = (await Promise.all(postPromises)).filter(Boolean);

    //     usersData.push({
    //       ...userData,
    //       // following: userData.bookmarks || [],
    //       // followers: (await getFollowersIds(userId)) || [],
    //       id: userSnap.id,
    //       posts,
    //     });
    //   }
    // }
    // if (filters) {
    //   usersData = await userFilterWrapper({ filters, users: usersData });
    // }

    // return { success: true, users: usersData };
  } catch (error) {
    console.error("Error fetching users:", error);
    return { success: false, users: [] };
  }
};

// 📌 Get starred posts for a specific user by their ID

export const getUserByPostId = async (eventId: string) => {
  try {
    const { db } = await initFirebase();
    const usersRef = collection(db, "users");
    const q = query(usersRef, where("posts", "array-contains", eventId));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const users = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
      return { success: true, users };
    } else {
      return { success: false, error: "No users found for this event" };
    }
  } catch (error) {
    console.error("Error fetching user:", error);
    return { success: false, error: "Server error" };
  }
};
// get user by event id
export const getUserByEventId = async (eventId: string) => {
  try {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "GetUserByEventId",
        payload: {
          eventId,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;

    // const { db } = await initFirebase();
    // // Query the users collection to find users whose events array contains the given eventId
    // const usersRef = collection(db, "users");
    // const q = query(usersRef, where("events", "array-contains", eventId));

    // const querySnapshot = await getDocs(q);

    // if (!querySnapshot.empty) {
    //   // If any user document matches the eventId in their events array, return the user data
    //   const users = querySnapshot.docs.map((doc) => ({
    //     id: doc.id,
    //     ...doc.data(),
    //   }));

    //   return { success: true, users }; // Return all matching users
    // } else {
    //   return { success: false, error: "No users found for this event" };
    // }
  } catch (error) {
    // console.error("Error fetching user:", error);
    return { success: false, error: "Server error" };
  }
};

// get user by services id

export const getUserByServicesId = async (eventId: string) => {
  try {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "GetUserByServicesId",
        payload: {
          eventId,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;

    // const { db } = await initFirebase();

    // // Query the users collection to find users whose events array contains the given eventId
    // const usersRef = collection(db, "users");
    // const q = query(usersRef, where("services", "array-contains", eventId));

    // const querySnapshot = await getDocs(q);

    // if (!querySnapshot.empty) {
    //   // If any user document matches the eventId in their events array, return the user data
    //   const users = querySnapshot.docs.map((doc) => ({
    //     id: doc.id,
    //     ...doc.data(),
    //   }));

    //   return { success: true, users }; // Return all matching users
    // } else {
    //   return { success: false, error: "No users found for this event" };
    // }
  } catch (error) {
    // console.error("Error fetching user:", error);
    return { success: false, error: "Server error" };
  }
};

// **Update User Service**
export const updateUser = async (userId: string | undefined, updatedData: any) => {
  try {
    if (!userId) {
      console.error("Error updating user: userId is undefined");
      return { success: false, error: "User ID is required" };
    }
    const idToken = await getIdToken();

    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${idToken}`,
      },
      body: JSON.stringify({
        type: "UpdateUser",
        payload: {
          userId,
          updatedData,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;

    // const { db } = await initFirebase();
    // const userRef = doc(db, "users", userId);
    // const userSnap = await getDoc(userRef);
    // if (userSnap.exists()) {
    //   const current_profile_name = userSnap.data().profile_name;

    //   console.log({ userId, updatedData, current_profile_name });
    //   if (Object.keys(updatedData).includes("profile_name") && updatedData?.["profile_name"]) {
    //     const usersRef = collection(db, "users");
    //     const q = query(usersRef, where("profile_name", "==", updatedData?.["profile_name"]));
    //     const snapshot = await getDocs(q);
    //     const data = snapshot?.docs?.[0]?.data();

    //     if (data?.id && data?.id !== userId) {
    //       return { success: false, error: "user name already exists!" };
    //     }
    //   }

    //   await updateDoc(userRef, updatedData);
    //   return { success: true };
    // }

    // return { success: false };
  } catch (error) {
    // console.error("Error updating user:", error);
    return { success: false, error: "Server error" };
  }
};

// add star post

export const toggleStarredPost = async (userId: string, postId: string, isStarred: boolean) => {
  try {
    const idToken = await getIdToken();
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${idToken}`,
      },
      body: JSON.stringify({
        type: "ToggleStarredPost",
        payload: {
          userId,
          postId,
          isStarred,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;

    // const { db } = await initFirebase();
    // const auth = getAuth();
    // const user = auth.currentUser;

    // if (!user?.uid) {
    //   return { success: false };
    // }
    // const userRef = doc(db, "users", userId);

    // await updateDoc(userRef, {
    //   starredPosts: isStarred ? arrayRemove(postId) : arrayUnion(postId),
    // });

    // // notification
    // if (
    //   !isStarred ||
    //   user.uid !== userId // no notification for own reaction
    // ) {
    //   const post_details = await GetPostDetailsByPostId({ post_id: postId });
    //   if (!post_details?.user_id || user?.uid === post_details?.user_id) {
    //     return { success: true };
    //   }
    //   NotificationManager.getInstance().CreateNotification({
    //     payload: {
    //       src_id: user?.uid,
    //       dest_id: post_details?.user_id,
    //       event: NotificationEvents.REACTION,
    //       post_id: postId,
    //       post_url: post_details?.postFile,
    //       thumbnail_url: post_details?.thumbnailUrl,
    //     },
    //   });
    // }

    // return { success: true };
  } catch (error) {
    return { success: false, error: "Server error" };
  }
};

// bookmark
// post_bookmarked
export const toggleBookMarks = async (userId: string, postId: string, isStarred: boolean) => {
  try {
    const idToken = await getIdToken();
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${idToken}`,
      },
      body: JSON.stringify({
        type: "ToggleBookMarks",
        payload: {
          userId,
          postId,
          isStarred,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;
    // const { db } = await initFirebase();
    // const auth = getAuth();
    // const user = auth.currentUser;

    // if (!user?.uid) {
    //   return { success: false };
    // }
    // const userRef = doc(db, "users", userId);
    // // console.log({ userId, postId, isStarred });

    // await updateDoc(userRef, {
    //   post_bookmarked: isStarred ? arrayRemove(postId) : arrayUnion(postId),
    // });

    // if (!isStarred) {
    //   const post_details = await GetPostDetailsByPostId({ post_id: postId });
    //   if (!post_details?.user_id || user?.uid === post_details?.user_id) {
    //     return { success: true };
    //   }

    //   NotificationManager.getInstance().CreateNotification({
    //     payload: {
    //       src_id: user?.uid,
    //       dest_id: post_details?.user_id,
    //       event: NotificationEvents.BOOKMARK,
    //       post_id: postId,
    //       post_url: post_details?.postFile,
    //       thumbnail_url: post_details?.thumbnailUrl,
    //     },
    //   });
    // }

    // return { success: true };
  } catch (error) {
    return { success: false, error: "Server error" };
  }
};

export const UpdateSocials = async ({
  user_id,
  type,
  url,
}: {
  user_id: string;
  type: "facebookLink" | "instagramLink" | "twitterLink" | "websiteLink" | "youtubeLink";
  url: string;
}) => {
  try {
    const idToken = await getIdToken();
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${idToken}`,
      },
      body: JSON.stringify({
        type: "UpdateSocials",
        payload: {
          user_id,
          type,
          url,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;
    // const { db } = await initFirebase();

    // const userRef = doc(db, USER_COLLECTION, user_id);

    // const resp = await updateDoc(userRef, {
    //   [type]: url,
    // });

    // // console.log(`${type} updated successfully`);

    // return "success";
  } catch (error) {
    console.log({ error });
    throw error;
  }
};

export const getUsersByCategory = async (category: any) => {
  try {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "GetUsersByCategory",
        payload: {
          category,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;
    // const { db } = await initFirebase();
    // const usersRef = collection(db, "users");
    // const q = query(usersRef, where("categories", "array-contains", category));
    // const querySnapshot = await getDocs(q);

    // const users = querySnapshot.docs
    //   .map((doc) => {
    //     const userData = doc.data();
    //     return userData.posts && userData.posts.length > 0 && userData?.isDeleted !== true // Ensure user has posts
    //       ? { id: doc.id, ...userData }
    //       : null;
    //   })
    //   .filter((user) => user !== null); // Remove users with no posts

    // return users;
  } catch (error) {
    console.error("Error fetching users:", error);
    return [];
  }
};

export const deleteUserDetails = async ({
  user_id,
  comment,
}: {
  user_id: string;
  comment: string;
}) => {
  try {
    const idToken = await getIdToken();
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${idToken}`,
      },
      body: JSON.stringify({
        type: "DeleteUserDetails",
        payload: {
          user_id,
          comment,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = await response.json();

    // const { auth } = await initFirebase();
    // const user = auth.currentUser;

    // if (!user) {
    //   throw new Error("user not detected please login again ");
    // }
    // let _user_id = user.uid;

    // if (user_id !== _user_id) {
    //   throw new Error("wrong user id sent");
    // }

    // const results = await Promise.allSettled([
    //   updateUserDeleteFlag({ user_id, flag: true, comment }),
    //   updatePostDeleteFlag({ user_id, flag: true }),
    //   updateEventsDeleteFlag({ user_id, flag: true }),
    //   updateServicesDeleteFlag({ user_id, flag: true }),
    //   // updateOrdersDeleteFlag({ user_id, flag: true }),
    //   // updateAuthBridgeDeleteFlag({ user_id }),
    //   updateFollowDeleteFlag({ user_id }), /// ℹ️ Need to update
    //   updateCommentsDeleteFlag({ user_id, flag: true }),
    // ]);

    // // Log the results for debugging
    // results.forEach((result, index) => {
    //   if (result.status === "rejected") {
    //     // console.error(Operation ${index + 1} failed:, result.reason);
    //   }
    // });

    // const resp = await user?.delete();
    // console.log({ resp });

    //    await MailServiceManager.getInstance()?.sendMail({
    //               toMail: "<EMAIL>",
    //               type:"profile_deleted",
    //               message:{
    //                profileId:user_id,
    //                reason:comment
    //               }
    //             });

    return {
      success: true,
      message: resp?.message,
    };
  } catch (error) {
    console.log({ error });
    throw new Error("delete user details failed");
  }
};

export const calculateProfileComplete = async ({ user_id }: { user_id: string }) => {
  try {
    //  const idToken = await getIdToken();
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Authorization: `Bearer ${idToken}`,
      },
      body: JSON.stringify({
        type: "CalculateProfileComplete",
        payload: {
          user_id,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = await response.json();
    return resp?.message;
    // //     profile_name 10 %
    // // about_me 10 %
    // // personal_moto 10 %
    // // avatar 20 %
    // // categories 30 % // ie added any categories
    // // posts 20% // ie added any post
    // const { db } = await initFirebase();
    // const userRef = doc(db, "users", user_id);
    // const userSnap = await getDoc(userRef);

    // if (!userSnap.exists()) {
    //   throw new Error("User not found");
    // }

    // const userData = userSnap.data();
    // let completionPercentage = 0;

    // // Check each field and add corresponding percentage
    // if (userData?.profile_name) completionPercentage += 10;
    // if (userData?.about_me) completionPercentage += 10;
    // if (userData?.personal_moto) completionPercentage += 10;
    // if (userData?.avatar) completionPercentage += 20;
    // if (userData?.categories && userData?.categories?.length > 0) completionPercentage += 30;
    // if (userData?.posts && userData?.posts?.length > 0) completionPercentage += 20;

    // return { success: true, completionPercentage };
  } catch (error) {
    console.log({ error });
  }
};

// email verification

export const getUsersByCategoryWithPost = async (
  categories: string[],
  filters?: Omit<GetFiltersInput, "date_of_publishing">
) => {
  try {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "GetUsersByCategoryWithPost",
        payload: {
          categories,
          filters,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;
    // const { db } = await initFirebase();

    // const usersRef = collection(db, "users");

    // // Since Firestore doesn't support "array-contains-any" in combination with post filtering later,
    // // we'll query users whose categories contain ANY of the specified categories
    // const q = query(usersRef, where("categories", "array-contains-any", categories));
    // const querySnapshot = await getDocs(q);

    // const usersWithPosts = await Promise.all(
    //   querySnapshot.docs.map(async (userDoc) => {
    //     const userData = userDoc.data();

    //     if (!userData.posts || userData.posts.length === 0) {
    //       return { id: userDoc.id, ...userData, posts: [] };
    //     }

    //     // Fetch all post data
    //     const postPromises = userData.posts.map(async (postId: string) => {
    //       const postRef = doc(db, "posts", postId);
    //       const postSnapshot = await getDoc(postRef);
    //       return postSnapshot.exists() ? { id: postSnapshot.id, ...postSnapshot.data() } : null;
    //     });

    //     let posts = (await Promise.all(postPromises)).filter(
    //       (post) => post !== null && categories.includes(post.category)
    //     );

    //     posts = posts.sort((a, b) => (b.added_at?.seconds || 0) - (a.added_at?.seconds || 0));

    //     return posts.length > 0 ? { id: userDoc.id, ...userData, posts } : null;
    //   })
    // );

    // let resp: any = usersWithPosts.filter((user) => user !== null);

    // if (filters) {
    //   resp = await userFilterWrapper({ filters, users: resp });
    // }

    // return resp;
  } catch (error) {
    console.error("Error fetching users and posts:", error);
    return [];
  }
};

export const sendVerificationEmail = async () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    return { success: false, error: "No user is signed in" };
  }
  if (!user?.email) {
    return { success: false, error: "User email not found " };
  }

  try {
    // await sendEmailVerification(user);
    await MailServiceManager.getInstance()?.sendMail({
      toMail: user?.email,
      type: "verify_email",
      message: {},
    });
    return { success: true, message: "Verification email sent successfully" };
  } catch (error) {
    console.error("Error sending verification email:", error);
    return { success: false, error: "Failed to send verification email" };
  }
};
export const sendResetPassword = async (email: string) => {
  if (!email) {
    return { success: false, error: "Email is required" };
  }

  try {
    // await sendPasswordResetEmail(auth, email);
    await MailServiceManager.getInstance()?.sendResetPasswordMail({
      toMail: email,
      type: "reset_password",
      message: {},
    });

    return { success: true, message: "Reset email sent successfully" };
  } catch (error) {
    console.error("Error sending Reset email:", error);
    return { success: false, error: "Failed to send Reset email" };
  }
};

export const isEmailVerified = () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    return { success: false, error: "No user is signed in" };
  }

  return { success: true, verified: user.emailVerified };
};

export const refreshUser = async () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    return { success: false, error: "No user is signed in" };
  }

  try {
    await user.reload(); // Reload user info from Firebase
    return { success: true, verified: user.emailVerified };
  } catch (error) {
    console.error("Error refreshing user:", error);
    return { success: false, error: "Failed to refresh user status" };
  }
};

export const getUniqueUserName = async (user_name?: string): Promise<string> => {
  //  const idToken = await getIdToken();
  const response = await fetch(GATE_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      // Authorization: `Bearer ${idToken}`,
    },
    body: JSON.stringify({
      type: "GetUniqueUserName",
      payload: {
        user_name,
      },
    }),
  });
  if (response.status !== 200) {
    const errorText = await response.text();
    console.log({ errorText });
  }
  let resp = await response.json();
  return resp?.message;
  // if (user_name) {
  //   const exists = await checkUserNameExists(user_name);
  //   if (!exists) return user_name;
  // }

  // let uniqueName: string;
  // let attempts = 0;

  // do {
  //   uniqueName = generateUsername();
  //   attempts++;
  //   if (attempts > 10) throw new Error("Too many attempts to generate a unique username");
  // } while (await checkUserNameExists(uniqueName));

  // return uniqueName;
};

export const GetSidebarCount = async () => {
  try {
    // const {auth} = await initFirebase();

    const user_id = // "W5437xR475cbUigqs5BlqWStrZw1"
      JSON?.parse(localStorage.getItem("user") ?? "")?.uid;

    if (!user_id)
      return {
        basketCount: 0,
        chatCount: 0,
        notificationCount: 0,
      };

    // Execute all in parallel
    const [orderRes, chatRes, notifCount] = await Promise.all([
      GetOrderDetailsByUserId({ userId: user_id }),
      ChatManager.getInstance().GetUserChatSummaries({ user_id }),
      NotificationManager.getInstance().UserUnreadNotificationCount({ user_id }),
    ]);

    return {
      basketCount: orderRes.basket?.length,
      myOrdersCount: orderRes?.my_orders?.length + orderRes?.received_orders?.length,
      chatCount: chatRes.totalUnreadCount,
      notificationCount: notifCount,
    };
  } catch (error) {
    console.log({ error });
    return {
      basketCount: 0,
      chatCount: 0,
      notificationCount: 0,
    };
  }
};

export const getUserIdByProfileName = async (profile_name: string): Promise<string | null> => {
  try {
    // const {appCheck} = await initFirebase();
    //   if(!appCheck) return null;
    // const {token} = await getToken(appCheck,false);

    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // "X-Firebase-AppCheck": token,
      },
      body: JSON.stringify({
        type: "GetUserIdByProfileName",
        payload: {
          profile_name,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;

    // const { db } = await initFirebase();

    // const q = query(collection(db, "users"), where("profile_name", "==", profile_name));

    // const querySnapshot = await getDocs(q);

    // if (querySnapshot.empty) {
    //   console.warn(`No user found with profile_name = ${profile_name}`);
    //   return null;
    // }

    // const userDoc = querySnapshot.docs[0];
    // const userData = userDoc.data();

    // return userData.id ?? userDoc.id; // prefer id field, fallback to Firestore doc ID
  } catch (error) {
    console.error("Error fetching user by profile_name:", error);
    return null;
  }
};

export const getProfileNameByUserId = async (user_id: string): Promise<string | null> => {
  try {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "GetProfileNameByUserId",
        payload: {
          user_id,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;

    // const { db } = await initFirebase();

    // const q = query(collection(db, "users"), where("id", "==", user_id));

    // const querySnapshot = await getDocs(q);

    // if (querySnapshot.empty) {
    //   console.warn(`No user found with id = ${user_id}`);
    //   return null;
    // }

    // const userDoc = querySnapshot.docs[0];
    // const userData = userDoc.data();

    // return userData.profile_name;
  } catch (error) {
    console.error("Error fetching user by profile_name:", error);
    return null;
  }
};

/**
 * Check if the logged-in user has a Stripe ID
 * Returns user data with stripe_id status
 */
export const checkUserStripeId = async (
  userId: string
): Promise<{
  success: boolean;
  hasStripeId: boolean;
  stripeId?: string;
  user?: User;
  error?: string;
}> => {
  try {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "CheckUserStripeId",
        payload: {
          userId,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;
    // const { db } = await initFirebase();
    // const userRef = doc(db, "users", userId);
    // const userSnap = await getDoc(userRef);

    // if (!userSnap.exists()) {
    //   return {
    //     success: false,
    //     hasStripeId: false,
    //     error: "User not found",
    //   };
    // }

    // const userData = userSnap.data() as User;
    // const hasStripeId = Boolean(userData.stripe_id);

    // return {
    //   success: true,
    //   hasStripeId,
    //   stripeId: userData.stripe_id,
    //   user: userData,
    // };
  } catch (error) {
    console.error("Error checking user Stripe ID:", error);
    return {
      success: false,
      hasStripeId: false,
      error: "Failed to check Stripe ID",
    };
  }
};

export const GetUserStripeId = async (uid: string): Promise<string | null> => {
  try {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "GetUserStripeId",
        payload: {
          uid,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let resp = (await response.json())?.message;
    return resp;

    // const { db } = await initFirebase();

    // const userRef = doc(db, "users", uid);
    // const userSnap = await getDoc(userRef);

    // if (!userSnap.exists()) {
    //   return null;
    // }

    // const userData = userSnap?.data() ?? {};
    // return userData.stripe_id ?? null;
  } catch (error) {
    console.error("GetUserStripeId failed:", error);
    throw new Error("GetUserStripeId failed");
  }
};

export const GetUserInfo = async (uid: string) => {
  try {
    // const idToken = await getIdToken();
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        //  Authorization: `Bearer ${idToken}`,
      },
      body: JSON.stringify({
        type: "GetUserInfo",
        payload: {
          uid,
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }
    let _resp = (await response.json())?.message;
    return _resp ?? {};

    // const { db } = await initFirebase();

    // const userRef = doc(db, "users", uid);
    // const userSnap = await getDoc(userRef);

    // if (!userSnap.exists()) {
    //   return null;
    // }

    // const userData = userSnap?.data() ?? {} ;
    // return userData ?? {};
  } catch (error) {
    console.error("GetUserStripeId failed:", error);
    throw new Error("GetUserStripeId failed");
  }
};
