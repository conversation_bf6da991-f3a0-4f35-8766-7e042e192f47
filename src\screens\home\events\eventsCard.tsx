"use client";
import { useCallback, useEffect, useRef, useState } from "react";
import { GlobalCardEvents } from "@/globalComponents/globalCardEvents";
import { getAllEvents, getEventsByCategory } from "@/services/eventsServices";
import { getUserByEventId, getUserById } from "@/services/usersServices";
import { themes } from "../../../../theme";
import { useRouter } from "next/navigation";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import EventCardSkeleton from "@/components/CardSkeleton/EventCardSkeleton";
import { useFilter } from "@/context/FilterContext";

const EventsCard = (props: any) => {
  const router = useRouter();
  const user = useAuth();
  const { filters, getServiceFilters } = useFilter();
  const { profileData } = useProfile(user?.userId || "");
  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [localLoading, setLocalLoading] = useState<boolean>(true);
  const [evenstData, setEvenstData] = useState([]);
  const [userLoading, setUserLoading] = useState<string | null>(null);

  // i have add new code here
  // Function to get user ID for checking against followers
  const handlegetEventsByCategory = async () => {
    setLoading(true);
    try {
      const currentCategory =
        props.themeProperties.title === "My Feed" ? "My Feed" : props.themeProperties.title;

      // Check if filters are applied and if current category is in selected categories
      if (filters.categories && filters.categories.length > 0) {
        if (!filters.categories.includes(currentCategory)) {
          // Current category is not in selected filters, show no data
          setEvenstData([]);
          setLoading(false);
          return;
        }
      }

      // Get complete filter object for service call
      const serviceFilters = getServiceFilters();

      const response = await getEventsByCategory(
        currentCategory,
        currentCategory === "My Feed" ? user.userId : "",
        serviceFilters
      );
      if (response.success) {
        const eventsdata: any = response.events;
        console.log({ eventsdata });
        setEvenstData(eventsdata);
        setLoading(false);
      }
    } catch (error) {
      console.error("Error fetching user ID:", error);
    }
  };

  useEffect(() => {
    handlegetEventsByCategory();
  }, [props, user.userId, filters]); // Ensures function runs only when dependencies change

  // Force initial loading state and ensure it persists
  useEffect(() => {
    // Always start with loading state true
    setLocalLoading(true);

    let timer: NodeJS.Timeout;

    // Only transition to non-loading state when data is ready and loading is false
    if (!loading && categoryData && Object.keys(categoryData).length > 0) {
      timer = setTimeout(() => {
        setLocalLoading(false);
      }, 1000); // Longer delay to ensure skeleton is visible
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [loading, categoryData]);

  // Function to get user category by event ID
  const getUserCategory = async (eventId: string) => {
    try {
      const response = await getUserByEventId(eventId);
      if (response.success) {
        const userData: any = response.users;
        return userData[0]?.categories[0]; // Return the category
      }
    } catch (error) {
      console.error("Error fetching user category:", error);
    }
    return null;
  };

  console.log({ evenstData });

  // Function to get user ID for checking against followers
  const getUserIDForCheck = async (eventId: string) => {
    try {
      const response = await getUserByEventId(eventId);
      if (response.success) {
        const userData: any = response.users;
        return userData[0]?.id; // Return the user ID
      }
    } catch (error) {
      console.error("Error fetching user ID:", error);
    }
    return null;
  };

  return (
    <>
      {loading ? (
        <div className="">
          {/* Show 3 skeleton cards stacked vertically */}
          {[1, 2, 3].map((num) => (
            <div key={num} className="mb-4">
              <EventCardSkeleton count={1} showGrid={false} />
            </div>
          ))}
        </div>
      ) : (
        <div className="w-full mt-0">
          {evenstData.map((item: any, index: number) => (
            <div key={index} className="mb-2">
              {Object.entries(themes).map(([_, innerThemeProperties]) => (
                <div key={innerThemeProperties.title}>
                  {(item.category === "Storytelling" ? "Literature" : item.category) ===
                    innerThemeProperties.title && (
                    <button
                      // onClick={() => handleEventClick(item.id)}
                      className="w-full text-left"
                      disabled={userLoading === item.id}
                      style={{ opacity: userLoading === item.id ? 0.6 : 1 }}
                    >
                      <GlobalCardEvents post={item} border={props.border} />
                    </button>
                  )}
                </div>
              ))}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default EventsCard;

// "use client";
// import { useCallback, useEffect, useRef, useState } from "react";
// import { GlobalCardEvents } from "@/globalComponents/globalCardEvents";
// import { getAllEvents, getEventsByCategory } from "@/services/eventsServices";
// import { getUserByEventId, getUserById } from "@/services/usersServices";
// import { themes } from "../../../../theme";
// import Link from "next/link";
// import { useRouter } from "next/navigation";
// import useAuth from "@/hook";
// import useProfile from "@/hook/profileData";
// import LoadingOverlay from "@/components/loadingOverlay";

// const EventsCard = (props: any) => {
//   const user = useAuth();
//   const [loading, setLoading] = useState<boolean>(true);

//   const [events, setEvents]: any = useState([]);

//   useEffect(() => {
//     const fetchAllServices = async () => {
//       setLoading(true);
//       if (props.themeProperties.title) {
//         const responsedan = await getEventsByCategory(
//           props.themeProperties.title === "My Feed"
//             ? "My Feed" // Pass "My Feed" as category_name
//             : props.themeProperties.title,
//           user?.userId // Add user ID as second parameter
//         );
//         setEvents(responsedan.events);
//         console.log({ responsedan });

//         setLoading(false);
//       }
//     };

//     fetchAllServices();
//   }, [props]);

//   return (
//     <>
//       {loading ? (
//         <LoadingOverlay isLoading={loading} />
//       ) : (
//         <div className="w-full mt-2 grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3">
//           {events.map((item: any, index: number) => (
//             <div key={index} className="mb-2 ">
//               {Object.entries(themes).map(([_, innerThemeProperties]) => (
//                 <div key={innerThemeProperties.title}>
//                   {(item.category === "Storytelling"
//                     ? "Literature"
//                     : item.category) === innerThemeProperties.title && (
//                     <Link href={`/profile/${item.user_id}?view=Services`}>
//                       <p>{item.name}</p>
//                       <GlobalCardEvents
//                         post={item}
//                         border={innerThemeProperties.backgroundColor}
//                       />
//                     </Link>
//                   )}
//                 </div>
//               ))}
//             </div>
//           ))}
//         </div>
//       )}
//     </>
//   );
// };

// export default EventsCard;
